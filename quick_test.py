#!/usr/bin/env python3
"""
快速测试头部检测功能
不需要RTSP流，使用摄像头或测试图像
"""

import cv2
import torch
import numpy as np
import logging
import argparse

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_with_webcam():
    """使用网络摄像头测试"""
    logger.info("使用网络摄像头测试头部检测...")
    
    try:
        # 加载模型
        model = torch.hub.load('ultralytics/yolov5', 'yolov5s', pretrained=True)
        
        if torch.cuda.is_available():
            model.to('cuda')
            model.half()  # 使用FP16
            logger.info("使用GPU加速")
        else:
            logger.info("使用CPU模式")
        
        model.conf = 0.4
        
        # 打开摄像头
        cap = cv2.VideoCapture(0)
        if not cap.isOpened():
            logger.error("无法打开摄像头")
            return False
        
        logger.info("按 'q' 退出, 按 's' 保存图像")
        
        frame_count = 0
        while True:
            ret, frame = cap.read()
            if not ret:
                break
            
            frame_count += 1
            
            # 检测
            results = model(frame)
            detections = results.pandas().xyxy[0]
            
            # 筛选人员检测
            person_detections = detections[detections['class'] == 0]
            person_count = len(person_detections)
            
            # 绘制结果
            for _, detection in person_detections.iterrows():
                x1, y1, x2, y2 = int(detection['xmin']), int(detection['ymin']), int(detection['xmax']), int(detection['ymax'])
                conf = detection['confidence']
                
                # 提取头部区域（人体上部15%）
                person_height = y2 - y1
                head_height = int(person_height * 0.15)
                head_x1, head_y1 = x1, y1
                head_x2, head_y2 = x2, y1 + head_height
                
                # 绘制人体框（绿色，细线）
                cv2.rectangle(frame, (x1, y1), (x2, y2), (0, 255, 0), 1)
                
                # 绘制头部框（红色，最细线）
                cv2.rectangle(frame, (head_x1, head_y1), (head_x2, head_y2), (0, 0, 255), 1)
                
                # 只显示数字标签
                person_num = _ + 1  # 使用循环索引
                cv2.putText(frame, f'{person_num}', (head_x1 + 2, head_y1 + 15), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.4, (255, 255, 255), 1)
            
            # 显示统计
            cv2.putText(frame, f'Heads: {person_count}', (10, 30), 
                       cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 0, 255), 2)
            
            cv2.putText(frame, f'Frame: {frame_count}', (10, 70), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 0, 0), 2)
            
            # 显示
            cv2.imshow('头部检测测试', frame)
            
            key = cv2.waitKey(1) & 0xFF
            if key == ord('q'):
                break
            elif key == ord('s'):
                cv2.imwrite(f'test_frame_{frame_count}.jpg', frame)
                logger.info(f"保存图像: test_frame_{frame_count}.jpg")
        
        cap.release()
        cv2.destroyAllWindows()
        logger.info("测试完成")
        return True
        
    except Exception as e:
        logger.error(f"测试失败: {e}")
        return False

def test_with_image():
    """使用测试图像"""
    logger.info("使用测试图像进行头部检测...")
    
    try:
        # 加载模型
        model = torch.hub.load('ultralytics/yolov5', 'yolov5s', pretrained=True)
        
        if torch.cuda.is_available():
            model.to('cuda')
            logger.info("使用GPU加速")
        
        # 创建测试图像（模拟人群场景）
        test_image = np.ones((480, 640, 3), dtype=np.uint8) * 128
        
        # 添加一些简单的人形轮廓
        cv2.rectangle(test_image, (100, 200), (150, 400), (100, 100, 200), -1)  # 人1
        cv2.rectangle(test_image, (200, 180), (250, 420), (100, 200, 100), -1)  # 人2
        cv2.rectangle(test_image, (350, 220), (400, 450), (200, 100, 100), -1)  # 人3
        
        # 添加头部区域
        cv2.circle(test_image, (125, 220), 25, (255, 200, 150), -1)  # 头1
        cv2.circle(test_image, (225, 200), 25, (255, 200, 150), -1)  # 头2
        cv2.circle(test_image, (375, 240), 25, (255, 200, 150), -1)  # 头3
        
        # 检测
        results = model(test_image)
        detections = results.pandas().xyxy[0]
        
        # 处理结果
        person_detections = detections[detections['class'] == 0]
        person_count = len(person_detections)
        
        logger.info(f"检测到 {person_count} 个人")
        
        # 绘制结果
        result_image = test_image.copy()
        for i, (_, detection) in enumerate(person_detections.iterrows()):
            x1, y1, x2, y2 = int(detection['xmin']), int(detection['ymin']), int(detection['xmax']), int(detection['ymax'])
            conf = detection['confidence']
            
            # 最细线条的边界框
            cv2.rectangle(result_image, (x1, y1), (x2, y2), (0, 255, 0), 1)
            
            # 只显示数字
            cv2.putText(result_image, f'{i+1}', (x1 + 2, y1 + 15), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.4, (255, 255, 255), 1)
        
        # 保存结果
        cv2.imwrite('test_detection_result.jpg', result_image)
        logger.info("测试结果保存为: test_detection_result.jpg")
        
        # 显示结果
        cv2.imshow('测试图像', test_image)
        cv2.imshow('检测结果', result_image)
        cv2.waitKey(0)
        cv2.destroyAllWindows()
        
        return True
        
    except Exception as e:
        logger.error(f"图像测试失败: {e}")
        return False

def main():
    parser = argparse.ArgumentParser(description='快速测试头部检测')
    parser.add_argument('--mode', choices=['webcam', 'image'], default='webcam',
                       help='测试模式: webcam(摄像头) 或 image(测试图像)')
    
    args = parser.parse_args()
    
    logger.info("🧪 开始快速测试...")
    
    if args.mode == 'webcam':
        success = test_with_webcam()
    else:
        success = test_with_image()
    
    if success:
        logger.info("✅ 测试成功！头部检测功能正常")
    else:
        logger.error("❌ 测试失败")
    
    return 0 if success else 1

if __name__ == "__main__":
    exit(main())