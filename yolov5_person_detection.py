#!/usr/bin/env python3
"""
YOLOv5 人数检测系统
用于检测RTSP视频流中的人数
"""

import cv2
import torch
import numpy as np
import time
from datetime import datetime
import argparse
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class YOLOv5PersonDetector:
    def __init__(self, model_path='yolov5s.pt', conf_threshold=0.5, device='cpu'):
        """
        初始化YOLOv5人员检测器
        
        Args:
            model_path: YOLOv5模型路径
            conf_threshold: 置信度阈值
            device: 运行设备 ('cpu' 或 'cuda')
        """
        self.conf_threshold = conf_threshold
        self.device = device
        
        # 加载YOLOv5模型
        try:
            self.model = torch.hub.load('ultralytics/yolov5', 'yolov5s', pretrained=True)
            self.model.to(device)
            self.model.conf = conf_threshold
            logger.info(f"YOLOv5模型加载成功，设备: {device}")
        except Exception as e:
            logger.error(f"模型加载失败: {e}")
            raise
        
        # COCO数据集中person类别的ID是0
        self.person_class_id = 0
        
    def detect_persons(self, frame):
        """
        检测帧中的人员
        
        Args:
            frame: 输入图像帧
            
        Returns:
            tuple: (人数, 检测结果, 带标注的图像)
        """
        try:
            # 使用YOLOv5进行推理
            results = self.model(frame)
            
            # 获取检测结果
            detections = results.pandas().xyxy[0]
            
            # 筛选出人员检测结果
            person_detections = detections[detections['class'] == self.person_class_id]
            person_count = len(person_detections)
            
            # 在图像上绘制检测框
            annotated_frame = frame.copy()
            for _, detection in person_detections.iterrows():
                x1, y1, x2, y2 = int(detection['xmin']), int(detection['ymin']), int(detection['xmax']), int(detection['ymax'])
                confidence = detection['confidence']
                
                # 绘制边界框
                cv2.rectangle(annotated_frame, (x1, y1), (x2, y2), (0, 255, 0), 2)
                
                # 添加标签
                label = f'Person: {confidence:.2f}'
                label_size = cv2.getTextSize(label, cv2.FONT_HERSHEY_SIMPLEX, 0.5, 2)[0]
                cv2.rectangle(annotated_frame, (x1, y1 - label_size[1] - 10), 
                            (x1 + label_size[0], y1), (0, 255, 0), -1)
                cv2.putText(annotated_frame, label, (x1, y1 - 5), 
                          cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 0, 0), 2)
            
            # 添加人数统计
            count_text = f'Person Count: {person_count}'
            cv2.putText(annotated_frame, count_text, (10, 30), 
                       cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 0, 255), 2)
            
            # 添加时间戳
            timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            cv2.putText(annotated_frame, timestamp, (10, annotated_frame.shape[0] - 10), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
            
            return person_count, person_detections, annotated_frame
            
        except Exception as e:
            logger.error(f"检测过程中出错: {e}")
            return 0, None, frame

class RTSPStreamProcessor:
    def __init__(self, rtsp_url, detector, save_video=False, output_path='output.mp4'):
        """
        初始化RTSP流处理器
        
        Args:
            rtsp_url: RTSP流地址
            detector: YOLOv5检测器实例
            save_video: 是否保存输出视频
            output_path: 输出视频路径
        """
        self.rtsp_url = rtsp_url
        self.detector = detector
        self.save_video = save_video
        self.output_path = output_path
        self.cap = None
        self.out = None
        
    def connect_stream(self):
        """连接RTSP流"""
        try:
            self.cap = cv2.VideoCapture(self.rtsp_url)
            
            # 设置缓冲区大小以减少延迟
            self.cap.set(cv2.CAP_PROP_BUFFERSIZE, 1)
            
            if not self.cap.isOpened():
                raise Exception("无法连接到RTSP流")
                
            # 获取视频属性
            fps = int(self.cap.get(cv2.CAP_PROP_FPS)) or 25
            width = int(self.cap.get(cv2.CAP_PROP_FRAME_WIDTH))
            height = int(self.cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
            
            logger.info(f"RTSP流连接成功: {width}x{height} @ {fps}fps")
            
            # 如果需要保存视频，初始化视频写入器
            if self.save_video:
                fourcc = cv2.VideoWriter_fourcc(*'mp4v')
                self.out = cv2.VideoWriter(self.output_path, fourcc, fps, (width, height))
                logger.info(f"视频将保存到: {self.output_path}")
                
            return True
            
        except Exception as e:
            logger.error(f"连接RTSP流失败: {e}")
            return False
    
    def process_stream(self, display=True, max_frames=None):
        """
        处理RTSP视频流
        
        Args:
            display: 是否显示视频窗口
            max_frames: 最大处理帧数（None表示无限制）
        """
        if not self.connect_stream():
            return
        
        frame_count = 0
        start_time = time.time()
        
        try:
            while True:
                ret, frame = self.cap.read()
                if not ret:
                    logger.warning("无法读取帧，尝试重新连接...")
                    time.sleep(1)
                    continue
                
                frame_count += 1
                
                # 检测人员
                person_count, detections, annotated_frame = self.detector.detect_persons(frame)
                
                # 计算FPS
                elapsed_time = time.time() - start_time
                fps = frame_count / elapsed_time if elapsed_time > 0 else 0
                
                # 添加FPS信息
                fps_text = f'FPS: {fps:.1f}'
                cv2.putText(annotated_frame, fps_text, (10, 70), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 0, 0), 2)
                
                # 显示结果
                if display:
                    cv2.imshow('YOLOv5 Person Detection', annotated_frame)
                    
                    # 按'q'退出
                    if cv2.waitKey(1) & 0xFF == ord('q'):
                        break
                
                # 保存视频
                if self.save_video and self.out:
                    self.out.write(annotated_frame)
                
                # 打印检测结果
                if frame_count % 30 == 0:  # 每30帧打印一次
                    logger.info(f"帧 {frame_count}: 检测到 {person_count} 人, FPS: {fps:.1f}")
                
                # 检查最大帧数限制
                if max_frames and frame_count >= max_frames:
                    break
                    
        except KeyboardInterrupt:
            logger.info("用户中断程序")
        except Exception as e:
            logger.error(f"处理过程中出错: {e}")
        finally:
            self.cleanup()
    
    def cleanup(self):
        """清理资源"""
        if self.cap:
            self.cap.release()
        if self.out:
            self.out.release()
        cv2.destroyAllWindows()
        logger.info("资源清理完成")

def main():
    parser = argparse.ArgumentParser(description='YOLOv5 RTSP人数检测系统')
    parser.add_argument('--rtsp-url', type=str, 
                       default='rtsp://root:Abc_123@*************:7040/axis-media/media.amp?resolution=1024x768',
                       help='RTSP流地址')
    parser.add_argument('--conf-threshold', type=float, default=0.5,
                       help='检测置信度阈值')
    parser.add_argument('--device', type=str, default='cpu',
                       choices=['cpu', 'cuda'], help='运行设备')
    parser.add_argument('--save-video', action='store_true',
                       help='保存输出视频')
    parser.add_argument('--output-path', type=str, default='output.mp4',
                       help='输出视频路径')
    parser.add_argument('--no-display', action='store_true',
                       help='不显示视频窗口')
    parser.add_argument('--max-frames', type=int, default=None,
                       help='最大处理帧数')
    
    args = parser.parse_args()
    
    # 检查CUDA可用性
    if args.device == 'cuda' and not torch.cuda.is_available():
        logger.warning("CUDA不可用，切换到CPU")
        args.device = 'cpu'
    
    try:
        # 初始化检测器
        logger.info("初始化YOLOv5检测器...")
        detector = YOLOv5PersonDetector(
            conf_threshold=args.conf_threshold,
            device=args.device
        )
        
        # 初始化流处理器
        logger.info("初始化RTSP流处理器...")
        processor = RTSPStreamProcessor(
            rtsp_url=args.rtsp_url,
            detector=detector,
            save_video=args.save_video,
            output_path=args.output_path
        )
        
        # 开始处理
        logger.info("开始处理RTSP流...")
        processor.process_stream(
            display=not args.no_display,
            max_frames=args.max_frames
        )
        
    except Exception as e:
        logger.error(f"程序运行出错: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    exit(main())