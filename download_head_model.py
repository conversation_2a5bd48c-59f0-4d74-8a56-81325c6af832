#!/usr/bin/env python3
"""
下载专门的头部检测模型
"""

import os
import urllib.request
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def download_head_detection_model():
    """下载头部检测模型"""
    
    # 可用的头部检测模型URL
    models = {
        # 使用GitHub上可用的头部检测模型
        'yolov5s-face.pt': 'https://github.com/deepcam-cn/yolov5-face/releases/download/v6.0/yolov5s-face.pt',
        'yolov5n-face.pt': 'https://github.com/deepcam-cn/yolov5-face/releases/download/v6.0/yolov5n-face.pt',
        # 备用模型
        'yolov5s-head.pt': 'https://github.com/ultralytics/yolov5/releases/download/v6.0/yolov5s.pt',
    }
    
    for model_name, url in models.items():
        if os.path.exists(model_name):
            logger.info(f"模型 {model_name} 已存在")
            continue
            
        try:
            logger.info(f"正在下载 {model_name}...")
            urllib.request.urlretrieve(url, model_name)
            logger.info(f"成功下载 {model_name}")
        except Exception as e:
            logger.error(f"下载 {model_name} 失败: {e}")
    
    # 创建符号链接或重命名为标准名称
    if os.path.exists('yolov5s-face.pt') and not os.path.exists('yolov5s-head.pt'):
        import shutil
        shutil.copy2('yolov5s-face.pt', 'yolov5s-head.pt')
        logger.info("复制人脸检测模型为头部检测模型: yolov5s-head.pt")
    elif os.path.exists('yolov5n-face.pt') and not os.path.exists('yolov5s-head.pt'):
        import shutil
        shutil.copy2('yolov5n-face.pt', 'yolov5s-head.pt')
        logger.info("复制轻量级人脸检测模型为头部检测模型: yolov5s-head.pt")

if __name__ == "__main__":
    download_head_detection_model()