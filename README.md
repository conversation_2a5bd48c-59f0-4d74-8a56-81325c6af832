# YOLOv5 RTSP头部检测系统

这是一个基于YOLOv5的实时头部检测系统，可以处理RTSP视频流并统计其中的头部数量。支持两种检测模式：专门的头部检测和基于人体检测的头部区域提取。

## 功能特点

- 🎯 基于YOLOv5的高精度人员检测
- 📹 支持RTSP视频流实时处理
- 📊 实时人数统计和显示
- 💾 可选的视频录制功能
- ⚡ 支持CPU和GPU加速
- 📝 详细的日志记录
- 🎨 可视化检测结果

## 安装依赖

```bash
pip install -r requirements.txt
```

## 使用方法

### 基本使用

```bash
python yolov5_person_detection.py
```

这将使用默认的RTSP地址开始检测。

### 自定义参数

```bash
python yolov5_person_detection.py \
    --rtsp-url "rtsp://your-camera-url" \
    --conf-threshold 0.6 \
    --device cuda \
    --save-video \
    --output-path "detection_output.mp4"
```

### 参数说明

- `--rtsp-url`: RTSP流地址（默认已配置您提供的地址）
- `--conf-threshold`: 检测置信度阈值（0.0-1.0，默认0.5）
- `--device`: 运行设备（cpu或cuda，默认cpu）
- `--save-video`: 保存检测结果视频
- `--output-path`: 输出视频文件路径
- `--no-display`: 不显示实时视频窗口
- `--max-frames`: 最大处理帧数（用于测试）

### 无界面运行

```bash
python yolov5_person_detection.py --no-display --save-video
```

## 系统要求

- Python 3.7+
- OpenCV 4.5+
- PyTorch 1.9+
- 足够的内存（建议8GB+）
- （可选）NVIDIA GPU + CUDA（用于GPU加速）

## 性能优化建议

1. **GPU加速**: 如果有NVIDIA GPU，使用`--device cuda`可显著提升性能
2. **置信度阈值**: 适当调整`--conf-threshold`可平衡检测精度和速度
3. **网络优化**: 确保网络连接稳定，减少RTSP流延迟
4. **系统资源**: 关闭不必要的程序以释放CPU和内存

## 故障排除

### 常见问题

1. **无法连接RTSP流**
   - 检查网络连接
   - 验证RTSP URL和认证信息
   - 确认摄像头支持指定的分辨率

2. **检测精度不佳**
   - 调整置信度阈值
   - 确保光照条件良好
   - 检查摄像头角度和距离

3. **性能问题**
   - 使用GPU加速
   - 降低视频分辨率
   - 调整检测频率

### 日志信息

程序会输出详细的日志信息，包括：
- 模型加载状态
- RTSP连接状态
- 实时检测结果
- 性能指标（FPS）

## 技术细节

- **模型**: YOLOv5s（预训练模型）
- **检测类别**: COCO数据集中的"person"类别
- **输入格式**: RTSP视频流
- **输出格式**: 带标注的视频帧 + 人数统计

## 扩展功能

可以根据需要添加以下功能：
- 人员轨迹跟踪
- 区域入侵检测
- 人数变化告警
- 数据库存储
- Web界面展示

## 许可证

本项目基于MIT许可证开源。