#!/usr/bin/env python3
"""
头部检测环境设置脚本
自动下载模型并配置环境
"""

import os
import sys
import subprocess
import logging
import urllib.request
import torch

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def check_requirements():
    """检查系统要求"""
    logger.info("=== 检查系统要求 ===")
    
    # 检查Python版本
    python_version = sys.version_info
    logger.info(f"Python版本: {python_version.major}.{python_version.minor}.{python_version.micro}")
    
    if python_version.major < 3 or (python_version.major == 3 and python_version.minor < 7):
        logger.error("需要Python 3.7或更高版本")
        return False
    
    # 检查PyTorch
    try:
        logger.info(f"PyTorch版本: {torch.__version__}")
        
        # 检查CUDA
        if torch.cuda.is_available():
            gpu_count = torch.cuda.device_count()
            gpu_name = torch.cuda.get_device_name(0)
            logger.info(f"CUDA可用: {gpu_count} 个GPU")
            logger.info(f"主GPU: {gpu_name}")
            logger.info(f"CUDA版本: {torch.version.cuda}")
        else:
            logger.warning("CUDA不可用，将使用CPU模式")
    except ImportError:
        logger.error("PyTorch未安装")
        return False
    
    return True

def install_requirements():
    """安装依赖包"""
    logger.info("=== 安装依赖包 ===")
    
    try:
        # 检查requirements.txt是否存在
        if os.path.exists('requirements.txt'):
            logger.info("安装requirements.txt中的依赖...")
            subprocess.check_call([sys.executable, '-m', 'pip', 'install', '-r', 'requirements.txt'])
        else:
            # 手动安装关键依赖
            packages = [
                'torch>=1.9.0',
                'torchvision>=0.10.0',
                'opencv-python>=4.5.0',
                'numpy>=1.21.0',
                'pandas>=1.3.0',
                'Pillow>=8.3.0',
                'PyYAML>=5.4.0',
                'requests>=2.25.0',
                'tqdm>=4.61.0',
                'matplotlib>=3.3.0',
                'ultralytics>=8.0.0'
            ]
            
            for package in packages:
                logger.info(f"安装 {package}...")
                subprocess.check_call([sys.executable, '-m', 'pip', 'install', package])
        
        logger.info("依赖包安装完成")
        return True
        
    except subprocess.CalledProcessError as e:
        logger.error(f"安装依赖包失败: {e}")
        return False

def download_models():
    """下载头部检测模型"""
    logger.info("=== 下载头部检测模型 ===")
    
    # 可用的模型列表
    models = [
        {
            'name': 'yolov5s-face.pt',
            'url': 'https://github.com/deepcam-cn/yolov5-face/releases/download/v6.0/yolov5s-face.pt',
            'description': '人脸检测模型（推荐用于头部检测）'
        },
        {
            'name': 'yolov5n-face.pt', 
            'url': 'https://github.com/deepcam-cn/yolov5-face/releases/download/v6.0/yolov5n-face.pt',
            'description': '轻量级人脸检测模型'
        },
        {
            'name': 'yolov5s.pt',
            'url': 'https://github.com/ultralytics/yolov5/releases/download/v6.0/yolov5s.pt',
            'description': '通用目标检测模型（备用）'
        }
    ]
    
    downloaded_models = []
    
    for model in models:
        model_path = model['name']
        
        if os.path.exists(model_path):
            logger.info(f"模型 {model_path} 已存在")
            downloaded_models.append(model_path)
            continue
        
        try:
            logger.info(f"下载 {model['description']}: {model_path}")
            
            # 显示下载进度
            def show_progress(block_num, block_size, total_size):
                if total_size > 0:
                    percent = min(100, (block_num * block_size * 100) // total_size)
                    if block_num % 100 == 0:  # 每100个块显示一次
                        print(f"\r下载进度: {percent}%", end='', flush=True)
            
            urllib.request.urlretrieve(model['url'], model_path, show_progress)
            print()  # 换行
            logger.info(f"成功下载 {model_path}")
            downloaded_models.append(model_path)
            
        except Exception as e:
            logger.warning(f"下载 {model_path} 失败: {e}")
            continue
    
    # 设置主要的头部检测模型
    if 'yolov5s-face.pt' in downloaded_models:
        if not os.path.exists('yolov5s-head.pt'):
            import shutil
            shutil.copy2('yolov5s-face.pt', 'yolov5s-head.pt')
            logger.info("设置 yolov5s-face.pt 为主要头部检测模型")
    elif 'yolov5n-face.pt' in downloaded_models:
        if not os.path.exists('yolov5s-head.pt'):
            import shutil
            shutil.copy2('yolov5n-face.pt', 'yolov5s-head.pt')
            logger.info("设置 yolov5n-face.pt 为主要头部检测模型")
    
    if downloaded_models:
        logger.info(f"成功下载 {len(downloaded_models)} 个模型")
        return True
    else:
        logger.warning("没有成功下载任何模型，将使用在线模型")
        return False

def test_detection():
    """测试检测功能"""
    logger.info("=== 测试检测功能 ===")
    
    try:
        # 创建测试脚本
        test_script = """
import torch
import cv2
import numpy as np

# 测试模型加载
try:
    model = torch.hub.load('ultralytics/yolov5', 'yolov5s', pretrained=True)
    print("✅ YOLOv5模型加载成功")
    
    # 测试GPU
    if torch.cuda.is_available():
        model.to('cuda')
        print("✅ GPU模式测试成功")
    else:
        print("⚠️  使用CPU模式")
    
    # 创建测试图像
    test_image = np.random.randint(0, 255, (640, 640, 3), dtype=np.uint8)
    
    # 测试推理
    results = model(test_image)
    print("✅ 模型推理测试成功")
    
    print("🎉 所有测试通过！")
    
except Exception as e:
    print(f"❌ 测试失败: {e}")
"""
        
        with open('tmp_rovodev_test.py', 'w', encoding='utf-8') as f:
            f.write(test_script)
        
        # 运行测试
        result = subprocess.run([sys.executable, 'tmp_rovodev_test.py'], 
                              capture_output=True, text=True, timeout=60)
        
        if result.returncode == 0:
            logger.info("检测功能测试通过")
            print(result.stdout)
        else:
            logger.warning("检测功能测试失败")
            print(result.stderr)
        
        # 清理测试文件
        if os.path.exists('tmp_rovodev_test.py'):
            os.remove('tmp_rovodev_test.py')
        
        return result.returncode == 0
        
    except Exception as e:
        logger.error(f"测试过程出错: {e}")
        return False

def create_run_script():
    """创建运行脚本"""
    logger.info("=== 创建运行脚本 ===")
    
    # Windows批处理脚本
    bat_content = """@echo off
echo 启动GPU优化头部检测...
python gpu_optimized_head_detection.py
pause
"""
    
    with open('run_gpu_head_detection.bat', 'w', encoding='utf-8') as f:
        f.write(bat_content)
    
    # Linux/Mac shell脚本
    sh_content = """#!/bin/bash
echo "启动GPU优化头部检测..."
python gpu_optimized_head_detection.py
"""
    
    with open('run_gpu_head_detection.sh', 'w', encoding='utf-8') as f:
        f.write(sh_content)
    
    # 设置执行权限（Linux/Mac）
    try:
        os.chmod('run_gpu_head_detection.sh', 0o755)
    except:
        pass
    
    logger.info("运行脚本创建完成")

def main():
    """主函数"""
    logger.info("🚀 开始设置头部检测环境...")
    
    # 检查系统要求
    if not check_requirements():
        logger.error("系统要求检查失败")
        return 1
    
    # 安装依赖
    if not install_requirements():
        logger.error("依赖安装失败")
        return 1
    
    # 下载模型
    download_models()
    
    # 测试功能
    if test_detection():
        logger.info("✅ 环境设置完成！")
    else:
        logger.warning("⚠️  环境设置完成，但测试未通过")
    
    # 创建运行脚本
    create_run_script()
    
    logger.info("=== 设置完成 ===")
    logger.info("现在您可以运行:")
    logger.info("  python gpu_optimized_head_detection.py")
    logger.info("或者双击运行:")
    logger.info("  run_gpu_head_detection.bat (Windows)")
    logger.info("  run_gpu_head_detection.sh (Linux/Mac)")
    
    return 0

if __name__ == "__main__":
    exit(main())