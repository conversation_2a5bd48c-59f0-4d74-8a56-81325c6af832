#!/usr/bin/env python3
"""
YOLOv5 头部检测系统
专门用于检测RTSP视频流中的人头数量
"""

import cv2
import torch
import numpy as np
import time
from datetime import datetime
import argparse
import logging
import urllib.request
import os

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class YOLOv5HeadDetector:
    def __init__(self, model_type='custom', conf_threshold=0.4, device='cpu'):
        """
        初始化YOLOv5头部检测器
        
        Args:
            model_type: 模型类型 ('custom' 使用头部检测模型, 'person' 使用人体检测)
            conf_threshold: 置信度阈值
            device: 运行设备 ('cpu' 或 'cuda')
        """
        self.conf_threshold = conf_threshold
        self.device = device
        self.model_type = model_type
        
        # 加载模型
        self._load_model()
        
        # 头部检测的类别ID（根据不同模型可能不同）
        self.head_class_names = ['head', 'person']  # 支持多种类别名称
        
    def _load_model(self):
        """加载YOLOv5模型"""
        try:
            if self.model_type == 'custom':
                # 尝试加载专门的头部检测模型
                logger.info("尝试加载头部检测模型...")
                try:
                    # 可以使用预训练的头部检测模型
                    self.model = torch.hub.load('ultralytics/yolov5', 'custom', 
                                               path='yolov5s-head.pt', force_reload=True)
                except:
                    logger.warning("未找到专门的头部检测模型，使用通用人体检测模型")
                    self.model = torch.hub.load('ultralytics/yolov5', 'yolov5s', pretrained=True)
                    self.model_type = 'person'
            else:
                # 使用标准的人体检测模型
                self.model = torch.hub.load('ultralytics/yolov5', 'yolov5s', pretrained=True)
            
            self.model.to(self.device)
            self.model.conf = self.conf_threshold
            logger.info(f"YOLOv5模型加载成功，设备: {self.device}, 类型: {self.model_type}")
            
        except Exception as e:
            logger.error(f"模型加载失败: {e}")
            raise
    
    def _extract_head_region(self, frame, person_bbox, head_ratio=0.15):
        """
        从人体检测框中提取头部区域
        
        Args:
            frame: 输入图像
            person_bbox: 人体边界框 [x1, y1, x2, y2]
            head_ratio: 头部占人体高度的比例
            
        Returns:
            头部区域的边界框 [x1, y1, x2, y2]
        """
        x1, y1, x2, y2 = person_bbox
        
        # 计算人体高度和宽度
        person_height = y2 - y1
        person_width = x2 - x1
        
        # 头部通常在人体上部的15-20%区域
        head_height = int(person_height * head_ratio)
        
        # 头部宽度通常比肩膀窄一些
        head_width_margin = int(person_width * 0.1)
        
        # 计算头部边界框
        head_x1 = max(0, x1 + head_width_margin)
        head_y1 = max(0, y1)
        head_x2 = min(frame.shape[1], x2 - head_width_margin)
        head_y2 = min(frame.shape[0], y1 + head_height)
        
        return [head_x1, head_y1, head_x2, head_y2]
    
    def detect_heads(self, frame):
        """
        检测帧中的头部
        
        Args:
            frame: 输入图像帧
            
        Returns:
            tuple: (头部数量, 检测结果, 带标注的图像)
        """
        try:
            # 使用YOLOv5进行推理
            results = self.model(frame)
            
            # 获取检测结果
            detections = results.pandas().xyxy[0]
            
            head_bboxes = []
            annotated_frame = frame.copy()
            
            if self.model_type == 'custom':
                # 如果是专门的头部检测模型，直接使用检测结果
                head_detections = detections[detections['name'].isin(['head', 'person'])]
                
                for _, detection in head_detections.iterrows():
                    x1, y1, x2, y2 = int(detection['xmin']), int(detection['ymin']), int(detection['xmax']), int(detection['ymax'])
                    confidence = detection['confidence']
                    head_bboxes.append([x1, y1, x2, y2, confidence])
                    
            else:
                # 如果是人体检测模型，从人体检测结果中提取头部区域
                person_detections = detections[detections['class'] == 0]  # person class
                
                for _, detection in person_detections.iterrows():
                    person_bbox = [int(detection['xmin']), int(detection['ymin']), 
                                 int(detection['xmax']), int(detection['ymax'])]
                    confidence = detection['confidence']
                    
                    # 提取头部区域
                    head_bbox = self._extract_head_region(frame, person_bbox)
                    head_bboxes.append(head_bbox + [confidence])
            
            # 在图像上绘制头部检测框
            head_count = len(head_bboxes)
            
            for i, (x1, y1, x2, y2, confidence) in enumerate(head_bboxes):
                # 绘制头部边界框（使用红色，最细线条）
                cv2.rectangle(annotated_frame, (x1, y1), (x2, y2), (0, 0, 255), 1)
                
                # 只显示数字编号
                label = f'{i+1}'
                label_size = cv2.getTextSize(label, cv2.FONT_HERSHEY_SIMPLEX, 0.4, 1)[0]
                cv2.rectangle(annotated_frame, (x1, y1 - label_size[1] - 6), 
                            (x1 + label_size[0] + 4, y1), (0, 0, 255), -1)
                cv2.putText(annotated_frame, label, (x1 + 2, y1 - 3), 
                          cv2.FONT_HERSHEY_SIMPLEX, 0.4, (255, 255, 255), 1)
                
                # 在头部中心画一个小圆点
                center_x = (x1 + x2) // 2
                center_y = (y1 + y2) // 2
                cv2.circle(annotated_frame, (center_x, center_y), 3, (255, 0, 0), -1)
            
            # 添加头部数量统计
            count_text = f'Head Count: {head_count}'
            cv2.putText(annotated_frame, count_text, (10, 30), 
                       cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 0, 255), 3)
            
            # 添加检测模式信息
            mode_text = f'Mode: {self.model_type.upper()} Detection'
            cv2.putText(annotated_frame, mode_text, (10, 70), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 165, 0), 2)
            
            # 添加时间戳
            timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            cv2.putText(annotated_frame, timestamp, (10, annotated_frame.shape[0] - 10), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
            
            return head_count, head_bboxes, annotated_frame
            
        except Exception as e:
            logger.error(f"头部检测过程中出错: {e}")
            return 0, [], frame

class RTSPHeadCounter:
    def __init__(self, rtsp_url, detector, save_video=False, output_path='head_detection_output.mp4'):
        """
        初始化RTSP头部计数器
        
        Args:
            rtsp_url: RTSP流地址
            detector: YOLOv5头部检测器实例
            save_video: 是否保存输出视频
            output_path: 输出视频路径
        """
        self.rtsp_url = rtsp_url
        self.detector = detector
        self.save_video = save_video
        self.output_path = output_path
        self.cap = None
        self.out = None
        
        # 统计信息
        self.total_frames = 0
        self.head_count_history = []
        
    def connect_stream(self):
        """连接RTSP流"""
        try:
            self.cap = cv2.VideoCapture(self.rtsp_url)
            
            # 设置缓冲区大小以减少延迟
            self.cap.set(cv2.CAP_PROP_BUFFERSIZE, 1)
            
            # 设置连接超时
            self.cap.set(cv2.CAP_PROP_OPEN_TIMEOUT_MSEC, 5000)
            self.cap.set(cv2.CAP_PROP_READ_TIMEOUT_MSEC, 5000)
            
            if not self.cap.isOpened():
                raise Exception("无法连接到RTSP流")
                
            # 获取视频属性
            fps = int(self.cap.get(cv2.CAP_PROP_FPS)) or 25
            width = int(self.cap.get(cv2.CAP_PROP_FRAME_WIDTH))
            height = int(self.cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
            
            logger.info(f"RTSP流连接成功: {width}x{height} @ {fps}fps")
            
            # 如果需要保存视频，初始化视频写入器
            if self.save_video:
                fourcc = cv2.VideoWriter_fourcc(*'mp4v')
                self.out = cv2.VideoWriter(self.output_path, fourcc, fps, (width, height))
                logger.info(f"头部检测视频将保存到: {self.output_path}")
                
            return True
            
        except Exception as e:
            logger.error(f"连接RTSP流失败: {e}")
            return False
    
    def get_statistics(self):
        """获取统计信息"""
        if not self.head_count_history:
            return {
                'total_frames': 0,
                'avg_head_count': 0,
                'max_head_count': 0,
                'min_head_count': 0
            }
        
        return {
            'total_frames': self.total_frames,
            'avg_head_count': np.mean(self.head_count_history),
            'max_head_count': max(self.head_count_history),
            'min_head_count': min(self.head_count_history)
        }
    
    def process_stream(self, display=True, max_frames=None, stats_interval=100):
        """
        处理RTSP视频流进行头部检测
        
        Args:
            display: 是否显示视频窗口
            max_frames: 最大处理帧数（None表示无限制）
            stats_interval: 统计信息输出间隔（帧数）
        """
        if not self.connect_stream():
            return
        
        start_time = time.time()
        
        try:
            while True:
                ret, frame = self.cap.read()
                if not ret:
                    logger.warning("无法读取帧，尝试重新连接...")
                    time.sleep(1)
                    continue
                
                self.total_frames += 1
                
                # 检测头部
                head_count, head_bboxes, annotated_frame = self.detector.detect_heads(frame)
                self.head_count_history.append(head_count)
                
                # 计算FPS
                elapsed_time = time.time() - start_time
                fps = self.total_frames / elapsed_time if elapsed_time > 0 else 0
                
                # 添加FPS和统计信息
                fps_text = f'FPS: {fps:.1f}'
                cv2.putText(annotated_frame, fps_text, (10, 110), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 0, 0), 2)
                
                # 添加平均头部数量
                if len(self.head_count_history) > 10:
                    avg_heads = np.mean(self.head_count_history[-30:])  # 最近30帧的平均值
                    avg_text = f'Avg Heads (30f): {avg_heads:.1f}'
                    cv2.putText(annotated_frame, avg_text, (10, 150), 
                               cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 255), 2)
                
                # 显示结果
                if display:
                    cv2.imshow('YOLOv5 Head Detection', annotated_frame)
                    
                    # 按键控制
                    key = cv2.waitKey(1) & 0xFF
                    if key == ord('q'):
                        break
                    elif key == ord('s'):  # 按's'保存当前帧
                        save_path = f"head_detection_frame_{self.total_frames}.jpg"
                        cv2.imwrite(save_path, annotated_frame)
                        logger.info(f"保存帧到: {save_path}")
                
                # 保存视频
                if self.save_video and self.out:
                    self.out.write(annotated_frame)
                
                # 定期打印统计信息
                if self.total_frames % stats_interval == 0:
                    stats = self.get_statistics()
                    logger.info(f"帧 {self.total_frames}: 当前 {head_count} 头, "
                              f"平均 {stats['avg_head_count']:.1f}, "
                              f"最大 {stats['max_head_count']}, "
                              f"FPS: {fps:.1f}")
                
                # 检查最大帧数限制
                if max_frames and self.total_frames >= max_frames:
                    break
                    
        except KeyboardInterrupt:
            logger.info("用户中断程序")
        except Exception as e:
            logger.error(f"处理过程中出错: {e}")
        finally:
            # 输出最终统计信息
            final_stats = self.get_statistics()
            logger.info("=== 最终统计信息 ===")
            logger.info(f"总处理帧数: {final_stats['total_frames']}")
            logger.info(f"平均头部数量: {final_stats['avg_head_count']:.2f}")
            logger.info(f"最大头部数量: {final_stats['max_head_count']}")
            logger.info(f"最小头部数量: {final_stats['min_head_count']}")
            
            self.cleanup()
    
    def cleanup(self):
        """清理资源"""
        if self.cap:
            self.cap.release()
        if self.out:
            self.out.release()
        cv2.destroyAllWindows()
        logger.info("资源清理完成")

def main():
    parser = argparse.ArgumentParser(description='YOLOv5 RTSP头部检测系统')
    parser.add_argument('--rtsp-url', type=str, 
                       default='rtsp://root:Abc_123@*************:7040/axis-media/media.amp?resolution=1024x768',
                       help='RTSP流地址')
    parser.add_argument('--model-type', type=str, default='person',
                       choices=['custom', 'person'], 
                       help='模型类型: custom(头部检测模型) 或 person(人体检测模型)')
    parser.add_argument('--conf-threshold', type=float, default=0.4,
                       help='检测置信度阈值')
    parser.add_argument('--device', type=str, default='cuda',
                       choices=['cpu', 'cuda'], help='运行设备')
    parser.add_argument('--save-video', action='store_true',
                       help='保存输出视频')
    parser.add_argument('--output-path', type=str, default='head_detection_output.mp4',
                       help='输出视频路径')
    parser.add_argument('--no-display', action='store_true',
                       help='不显示视频窗口')
    parser.add_argument('--max-frames', type=int, default=None,
                       help='最大处理帧数')
    parser.add_argument('--stats-interval', type=int, default=100,
                       help='统计信息输出间隔（帧数）')
    
    args = parser.parse_args()
    
    # 检查CUDA可用性并设置GPU
    if args.device == 'cuda':
        if torch.cuda.is_available():
            gpu_count = torch.cuda.device_count()
            gpu_name = torch.cuda.get_device_name(0)
            logger.info(f"检测到 {gpu_count} 个GPU: {gpu_name}")
            logger.info(f"CUDA版本: {torch.version.cuda}")
            logger.info(f"使用GPU加速进行头部检测")
        else:
            logger.warning("CUDA不可用，切换到CPU")
            args.device = 'cpu'
    else:
        logger.info("使用CPU进行头部检测")
    
    try:
        # 初始化头部检测器
        logger.info("初始化YOLOv5头部检测器...")
        detector = YOLOv5HeadDetector(
            model_type=args.model_type,
            conf_threshold=args.conf_threshold,
            device=args.device
        )
        
        # 初始化流处理器
        logger.info("初始化RTSP头部计数器...")
        counter = RTSPHeadCounter(
            rtsp_url=args.rtsp_url,
            detector=detector,
            save_video=args.save_video,
            output_path=args.output_path
        )
        
        # 开始处理
        logger.info("开始头部检测...")
        counter.process_stream(
            display=not args.no_display,
            max_frames=args.max_frames,
            stats_interval=args.stats_interval
        )
        
    except Exception as e:
        logger.error(f"程序运行出错: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    exit(main())